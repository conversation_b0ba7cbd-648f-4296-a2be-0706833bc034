package com.webank.maling.documentation.service;

import com.webank.maling.ai.documentation.AIDocumentationService;
import com.webank.maling.ai.documentation.DocumentationGenerationContext;
import com.webank.maling.base.config.AppConfig;
import com.webank.maling.base.model.MethodInfo;
import com.webank.maling.documentation.config.DocumentationConfig;
import com.webank.maling.base.model.SubgraphData;
import com.webank.maling.documentation.dto.DocumentationGenerationDto;
import com.webank.maling.documentation.entity.Documentation;
import com.webank.maling.documentation.entity.DocumentationTask;
import com.webank.maling.documentation.repository.graph.SubgraphRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * 渐进式文档生成服务
 * 负责管理分层次的文档生成流程
 *
 * <AUTHOR> Graph Team
 */
@Slf4j
@Service
public class ProgressiveDocumentationService {

    private final DocumentationConfig config;
    private final SubgraphRepository subgraphRepository;
    private final AIDocumentationService aiDocumentationService;
    private final DocumentationArchiveService archiveService;
    private final DocumentationTaskService taskService;

    @Autowired
    public ProgressiveDocumentationService(DocumentationConfig config,
                                         SubgraphRepository subgraphRepository,
                                         DocumentationArchiveService archiveService,
                                         DocumentationTaskService taskService) {
        this.config = config;
        this.subgraphRepository = subgraphRepository;
        this.aiDocumentationService = AIDocumentationService.getInstance();
        this.archiveService = archiveService;
        this.taskService = taskService;
    }
    
    /**
     * 启动渐进式文档生成
     *
     * @param dto 请求体
     * @param entryPoint 入口点信息
     * @return 任务ID
     */
    @Async("malingDocumentTaskExecutor")
    public CompletableFuture<Long> startProgressiveGeneration(DocumentationGenerationDto dto, MethodInfo entryPoint) {
        try {
            Integer level = dto.getLevel();
            String methodId = entryPoint.getMethodId();
            log.info("开始为入口点 {} 生成 {} 层级的渐进式文档", methodId, level);

            // 1. 创建生成任务
            DocumentationTask task = taskService.createTask(dto, entryPoint);

            // 2. 异步执行渐进式生成
            executeProgressiveGeneration(task);

            return CompletableFuture.completedFuture(task.getId());

        } catch (Exception e) {
            log.error("启动渐进式文档生成失败", e);
            return CompletableFuture.failedFuture(new RuntimeException("启动生成任务失败", e));
        }
    }
    
    /**
     * 执行渐进式生成流程
     */
    private void executeProgressiveGeneration(DocumentationTask task) {
        CompletableFuture.runAsync(() -> {
            try {
                taskService.updateTaskStatus(task.getId(), DocumentationTask.TaskStatus.RUNNING);
                
                Documentation previousDoc = null;
                
                // 逐层生成文档
                for (int level = 1; level <= task.getTargetLevel(); level++) {
                    log.info("开始生成第 {} 层文档，入口点: {}", level, task.getEntryPointId());
                    
                    // 更新任务进度
                    int progress = (level - 1) * 100 / task.getTargetLevel();
                    taskService.updateTaskProgress(task.getId(), level, progress);
                    
                    // 生成当前层级的文档
                    Documentation currentDoc = generateDocumentationForLevel(
                            task.getEntryPointId(), level, previousDoc);
                    
                    if (currentDoc == null) {
                        throw new RuntimeException("第 " + level + " 层文档生成失败");
                    }
                    
                    // 保存中间态文档
                    saveIntermediateDocumentation(currentDoc, level < task.getTargetLevel());
                    
                    previousDoc = currentDoc;
                    
                    log.info("第 {} 层文档生成完成，文档ID: {}", level, currentDoc.getId());
                }
                
                // 标记最终版本
                if (previousDoc != null) {
                    markAsFinalVersion(previousDoc);
                    
                    // 异步归档中间态版本
                    if (config.isAutoArchiveEnabled() && task.getTargetLevel() > 1) {
                        archiveService.archiveIntermediateVersions(task.getEntryPointId(), previousDoc);
                    }
                }
                
                // 完成任务
                taskService.completeTask(task.getId());
                log.info("渐进式文档生成完成，入口点: {}, 最终层级: {}", 
                        task.getEntryPointId(), task.getTargetLevel());
                
            } catch (Exception e) {
                log.error("渐进式文档生成失败，任务ID: {}", task.getId(), e);
                taskService.failTask(task.getId(), e.getMessage());
            }
        });
    }
    
    /**
     * 为指定层级生成文档
     */
    private Documentation generateDocumentationForLevel(String entryPointId, int level, 
                                                       Documentation previousDoc) {
        try {
            // 1. 获取当前层级的子图数据
            int maxSteps = config.getMaxStepsForLevel(level);
            SubgraphData subgraph = subgraphRepository.getSubgraphByLevel(entryPointId, maxSteps);
            
            if (subgraph.isEmpty()) {
                log.warn("入口点 {} 的第 {} 层子图为空", entryPointId, level);
                return null;
            }
            
            // 2. 构建生成上下文
            DocumentationGenerationContext context = DocumentationGenerationContext.builder()
                    .entryPointId(entryPointId)
                    .level(level)
                    .subgraph(subgraph)
                    .previousDocumentation(previousDoc)
                    .maxLength(config.getMaxLengthForLevel(level))
                    .build();
            
            // 3. 调用AI生成文档
            String content = aiDocumentationService.generateDocumentationForLevel(context);
            
            if (content == null || content.trim().isEmpty()) {
                log.error("AI生成的第 {} 层文档内容为空", level);
                return null;
            }
            
            // 4. 创建文档对象
            return Documentation.builder()
                    .entryPointId(entryPointId)
                    .entryPointName(subgraph.getEntryPoint().getMethodName())
                    .title(generateTitle(subgraph.getEntryPoint().getMethodName(), level))
                    .content(content)
                    .level(level)
                    .status(Documentation.DocumentationStatus.COMPLETED)
                    .version(1)
                    .isFinalVersion(false) // 初始都是中间态
                    .parentDocumentationId(previousDoc != null ? previousDoc.getId() : null)
                    .projectId(previousDoc.getProjectId())
                    .branchName(previousDoc.getBranchName())
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
                    
        } catch (Exception e) {
            log.error("生成第 {} 层文档时发生错误", level, e);
            return null;
        }
    }
    
    /**
     * 保存中间态文档
     */
    private void saveIntermediateDocumentation(Documentation documentation, boolean isIntermediate) {
        documentation.setIsFinalVersion(!isIntermediate);
        
        // TODO: 实现文档保存逻辑
        // saveDocumentation(documentation);
        
        // 同时保存方法信息
        saveDocumentationMethods(documentation);
        
        log.debug("保存文档，ID: {}, 层级: {}, 是否中间态: {}", 
                documentation.getId(), documentation.getLevel(), isIntermediate);
    }
    
    /**
     * 标记为最终版本
     */
    private void markAsFinalVersion(Documentation documentation) {
        documentation.setIsFinalVersion(true);
        documentation.setUpdatedAt(LocalDateTime.now());
        
        // TODO: 实现更新文档逻辑
        // updateDocumentation(documentation);
        
        log.info("文档已标记为最终版本，ID: {}", documentation.getId());
    }
    
    /**
     * 保存文档关联的方法信息
     */
    private void saveDocumentationMethods(Documentation documentation) {
        // TODO: 实现保存方法信息的逻辑
        // 从子图数据中提取方法信息并保存到DocumentationMethod表
    }
    
    /**
     * 生成文档标题
     */
    private String generateTitle(String methodName, int level) {
        String levelDesc = switch (level) {
            case 1 -> "核心流程";
            case 2 -> "详细流程";
            case 3 -> "完整文档";
            default -> "文档";
        };
        
        return String.format("%s - %s说明书", methodName, levelDesc);
    }
}
